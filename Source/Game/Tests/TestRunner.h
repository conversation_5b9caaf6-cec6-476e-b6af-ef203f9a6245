// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "TestConfig.h"
#include "Subsystems/WorldSubsystem.h"
#include "TestRunner.generated.h"

// 前向声明
class UPerformanceOptimizer;
class UCacheManager;
class UGPUComputeManager;
class UObjectPoolManager;
struct FPerformanceOptimizationParams;

/**
 * 【测试运行器委托】
 * 用于测试完成时的回调通知
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestCompleted, const FString&, TestName, const FTestResultDetail&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestSuiteCompleted, const TArray<FTestResultDetail>&, Results, const FTestSuiteConfig&, Config);

/**
 * 【测试运行器子系统】
 * 
 * 统一管理和执行所有性能优化相关的单元测试。
 * 提供测试调度、结果收集、报告生成等功能。
 * 
 * 主要功能：
 * - 单元测试执行和管理
 * - 性能基准测试
 * - 测试结果收集和分析
 * - 自动化测试报告生成
 * - CI/CD集成支持
 * 
 * 设计原则：
 * - 继承自UWorldSubsystem，符合UE5架构规范
 * - 支持异步测试执行
 * - 提供详细的测试统计和报告
 * - 支持不同测试模式和配置
 */
UCLASS(MinimalAPI)
class UTestRunner : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    /**
     * 【静态访问方法】获取测试运行器实例
     * 
     * @param World 世界对象指针
     * @return 测试运行器实例，如果世界无效则返回nullptr
     */
    GAME_API static UTestRunner* Get(const UWorld* World);

    /**
     * 【主要接口】运行完整的测试套件
     * 
     * @param Config 测试配置
     * @return 是否成功启动测试
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API bool RunTestSuite(const FTestSuiteConfig& Config = FTestSuiteConfig());

    /**
     * 【主要接口】运行单个测试类别
     * 
     * @param TestCategory 测试类别名称
     * @param Config 测试配置
     * @return 是否成功启动测试
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API bool RunTestCategory(const FString& TestCategory, const FTestSuiteConfig& Config = FTestSuiteConfig());

    /**
     * 【主要接口】运行性能基准测试
     * 
     * @param Config 测试配置
     * @return 是否成功启动测试
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API bool RunPerformanceBenchmarks(const FTestSuiteConfig& Config = FTestSuiteConfig());

    /**
     * 【控制接口】停止当前运行的测试
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API void StopCurrentTests();

    /**
     * 【查询接口】检查是否有测试正在运行
     * 
     * @return 是否有测试正在运行
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API bool IsTestRunning() const;

    /**
     * 【查询接口】获取当前测试进度
     * 
     * @return 测试进度（0.0-1.0）
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API float GetTestProgress() const;

    /**
     * 【结果接口】获取最近的测试结果
     * 
     * @return 测试结果数组
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API TArray<FTestResultDetail> GetLastTestResults() const;

    /**
     * 【结果接口】生成测试报告
     * 
     * @param Results 测试结果数组
     * @param Config 测试配置
     * @return 测试报告字符串
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API FString GenerateTestReport(const TArray<FTestResultDetail>& Results, const FTestSuiteConfig& Config) const;

    /**
     * 【结果接口】保存测试结果到文件
     * 
     * @param Results 测试结果数组
     * @param FilePath 文件路径
     * @return 是否保存成功
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API bool SaveTestResults(const TArray<FTestResultDetail>& Results, const FString& FilePath) const;

    /**
     * 【配置接口】设置测试配置
     * 
     * @param NewConfig 新的测试配置
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API void SetTestConfig(const FTestSuiteConfig& NewConfig);

    /**
     * 【配置接口】获取当前测试配置
     * 
     * @return 当前测试配置
     */
    UFUNCTION(BlueprintCallable, Category = "Test Runner")
    GAME_API FTestSuiteConfig GetTestConfig() const;

    /**
     * 【事件】单个测试完成回调
     */
    UPROPERTY(BlueprintAssignable, Category = "Test Runner")
    FOnTestCompleted OnTestCompleted;

    /**
     * 【事件】测试套件完成回调
     */
    UPROPERTY(BlueprintAssignable, Category = "Test Runner")
    FOnTestSuiteCompleted OnTestSuiteCompleted;

protected:
    /**
     * 【生命周期】子系统初始化
     */
    GAME_API virtual void Initialize(FSubsystemCollectionBase& Collection) override;

    /**
     * 【生命周期】子系统反初始化
     */
    GAME_API virtual void Deinitialize() override;

    /**
     * 【生命周期】条件创建检查
     */
    GAME_API virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

private:
    /** 【配置】当前测试配置 */
    UPROPERTY()
    FTestSuiteConfig CurrentConfig;

    /** 【状态】是否有测试正在运行 */
    UPROPERTY()
    bool bIsTestRunning = false;

    /** 【状态】当前测试进度 */
    UPROPERTY()
    float CurrentTestProgress = 0.0f;

    /** 【结果】最近的测试结果 */
    UPROPERTY()
    TArray<FTestResultDetail> LastTestResults;

    /** 【状态】当前运行的测试数量 */
    UPROPERTY()
    int32 CurrentTestCount = 0;

    /** 【状态】总测试数量 */
    UPROPERTY()
    int32 TotalTestCount = 0;

    /** 【引用】性能优化器引用 */
    UPROPERTY()
    TObjectPtr<UPerformanceOptimizer> PerformanceOptimizer;

    /** 【引用】缓存管理器引用 */
    UPROPERTY()
    TObjectPtr<UCacheManager> CacheManager;

    /** 【引用】GPU计算管理器引用 */
    UPROPERTY()
    TObjectPtr<UGPUComputeManager> GPUComputeManager;

    /** 【引用】对象池管理器引用 */
    UPROPERTY()
    TObjectPtr<UObjectPoolManager> ObjectPoolManager;

    // ========== 私有测试执行方法 ==========

    /**
     * 【内部方法】执行性能优化器测试
     * 
     * @param Config 测试配置
     * @return 测试结果数组
     */
    TArray<FTestResultDetail> RunPerformanceOptimizerTests(const FTestSuiteConfig& Config);

    /**
     * 【内部方法】执行缓存管理器测试
     * 
     * @param Config 测试配置
     * @return 测试结果数组
     */
    TArray<FTestResultDetail> RunCacheManagerTests(const FTestSuiteConfig& Config);

    /**
     * 【内部方法】执行GPU计算管理器测试
     * 
     * @param Config 测试配置
     * @return 测试结果数组
     */
    TArray<FTestResultDetail> RunGPUComputeManagerTests(const FTestSuiteConfig& Config);

    /**
     * 【内部方法】执行对象池管理器测试
     * 
     * @param Config 测试配置
     * @return 测试结果数组
     */
    TArray<FTestResultDetail> RunObjectPoolManagerTests(const FTestSuiteConfig& Config);

    /**
     * 【内部方法】执行性能基准测试
     * 
     * @param Config 测试配置
     * @return 测试结果数组
     */
    TArray<FTestResultDetail> RunBenchmarkTests(const FTestSuiteConfig& Config);

    // ========== 私有辅助方法 ==========

    /**
     * 【辅助方法】创建测试结果详情
     * 
     * @param TestName 测试名称
     * @param Status 测试状态
     * @param ExecutionTime 执行时间
     * @param MemoryUsage 内存使用
     * @param ErrorMessage 错误消息
     * @return 测试结果详情
     */
    FTestResultDetail CreateTestResult(
        const FString& TestName,
        ETestResultStatus Status,
        float ExecutionTime = 0.0f,
        float MemoryUsage = 0.0f,
        const FString& ErrorMessage = TEXT("")
    );

    /**
     * 【辅助方法】测量函数执行时间
     * 
     * @param TestFunction 要测量的函数
     * @return 执行时间（毫秒）
     */
    template<typename FunctionType>
    double MeasureExecutionTime(FunctionType TestFunction);

    /**
     * 【辅助方法】获取当前内存使用量
     * 
     * @return 内存使用量（MB）
     */
    float GetCurrentMemoryUsageMB();

    /**
     * 【辅助方法】更新测试进度
     * 
     * @param CompletedTests 已完成的测试数量
     * @param TotalTests 总测试数量
     */
    void UpdateTestProgress(int32 CompletedTests, int32 TotalTests);

    /**
     * 【辅助方法】通知测试完成
     * 
     * @param TestName 测试名称
     * @param Result 测试结果
     */
    void NotifyTestCompleted(const FString& TestName, const FTestResultDetail& Result);

    /**
     * 【辅助方法】通知测试套件完成
     * 
     * @param Results 所有测试结果
     * @param Config 测试配置
     */
    void NotifyTestSuiteCompleted(const TArray<FTestResultDetail>& Results, const FTestSuiteConfig& Config);

    /**
     * 【辅助方法】初始化测试环境
     * 
     * @return 是否初始化成功
     */
    bool InitializeTestEnvironment();

    /**
     * 【辅助方法】清理测试环境
     */
    void CleanupTestEnvironment();

    /**
     * 【辅助方法】验证测试前置条件
     * 
     * @param Config 测试配置
     * @return 是否满足前置条件
     */
    bool ValidateTestPreconditions(const FTestSuiteConfig& Config);
};
