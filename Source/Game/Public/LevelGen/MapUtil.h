#pragma once

#include "CoreMinimal.h"

#include "Engine/Engine.h"

#include "MapUtil.generated.h"


/**
 * 可视化状态枚举
 */
UENUM(BlueprintType)
enum class EVisualizationState : uint8
{
    Hidden = 0,         // 隐藏
    Visible,            // 可见
    Transitioning,      // 过渡中
    MAX UMETA(Hidden)
};
/**
 * 地图层级枚举
 */
UENUM(BlueprintType)
enum class EMapLayer : uint8
{
    World = 0,      // 世界层（大陆级别）
    Region,         // 区域层（城市级别）
    Building,       // 建筑层（房间级别）
    MAX UMETA(Hidden)
};

/**
 * 土壤类型枚举
 */
UENUM(BlueprintType)
enum class ESoilType : uint8
{
    None = 0,
    Loam,           // 壤土
    Sand,           // 砂土
    Clay,           // 黏土
    VolcanicAsh,    // 火山灰土
    Rock,           // 岩石
    Peat,           // 泥炭土
    Gravel,         // 砾石
    Bedrock,        // 基岩
    MAX UMETA(Hidden)
};

/**
 * 生态区类型枚举
 */
UENUM(BlueprintType)
enum class EBiomeType : uint8
{
    None = 0,
    Ocean,          // 海洋
    Desert,         // 沙漠
    Grassland,      // 草原
    Forest,         // 森林
    Mountain,       // 山地
    Tundra,         // 苔原
    Swamp,          // 沼泽
    Volcano,        // 火山
    Glacier,        // 冰川
    River,
    Lake,
    Jungle,
    Cave,
    MAX UMETA(Hidden)
};

/**
 * 地表覆盖类型枚举
 */
UENUM(BlueprintType)
enum class ESurfaceCoverType : uint8
{
    None = 0,
    Grass,          // 草地
    Rock,           // 岩石
    Sand,           // 沙地
    Snow,           // 雪地
    Ice,            // 冰面
    Water,          // 水面
    Mud,            // 泥地
    Lava,           // 熔岩
    BareGround,
    Dust,
    Leaves,
    Blood,
    Moss,
    MAX UMETA(Hidden)
};

/**
 * 地下空间类型枚举
 */
UENUM(BlueprintType)
enum class EUndergroundType : uint8
{
    None = 0,
    Cave,           // 洞穴
    LavaTube,       // 熔岩洞
    UndergroundRiver, // 地下河
    Dungeon,        // 地牢
    Mine,           // 矿井
    Tomb,           // 古墓
    Sewer,          // 下水道
    Basement,       // 地下室
    Tunnel,
    DeepCave,
    Crypt,
    MAX UMETA(Hidden)
};

/**
 * 道路类型枚举
 */
UENUM(BlueprintType)
enum class ERoadType : uint8
{
    None = 0,
    DirtPath,       // 土路
    StonePath,      // 石路
    WoodenBridge,   // 木桥
    StoneBridge,    // 石桥
    Highway,        // 大路
    Trail,          // 小径
    River,          // 河流（水路）
    UndergroundTunnel,
    MagicBridge,
    LavaBridge,
    MAX UMETA(Hidden)
};

/**
 * 地图生成阶段枚举
 */
UENUM(BlueprintType)
enum class EMapGenerationPhase : uint8
{
    None = 0,
    PhysicalLayer,      // 物理层生成
    BiomeMapping,       // 生态区映射
    NarrativeLayer,     // 叙事层集成
    RoadGeneration,     // 道路生成
    UndergroundGeneration, // 地下空间生成
    LayerScaling,       // 层级缩放
    DynamicChanges,     // 动态变化
    PerformanceOptimization, // 性能优化
    Visualization,      // 可视化
    Complete,           // 完成
    Optimization,
    UndergroundSpace,
    MAX UMETA(Hidden)
};


/**
 * 元素权重结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FElementalWeights
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elements")
    float Fire = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elements")
    float Water = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elements")
    float Earth = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elements")
    float Air = 0.0f;

    FElementalWeights()
    {
        Fire = 0.0f;
        Water = 0.0f;
        Earth = 0.0f;
        Air = 0.0f;
    }

    // 获取总权重
    float GetTotalWeight() const
    {
        return Fire + Water + Earth + Air;
    }

    // 归一化权重
    void Normalize()
    {
        float Total = GetTotalWeight();
        if (Total > 0.0f)
        {
            Fire /= Total;
            Water /= Total;
            Earth /= Total;
            Air /= Total;
        }
    }
};




USTRUCT(BlueprintType)
struct GAME_API FBiomeArchetype
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    EBiomeType BiomeType = EBiomeType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Parameters")
    float MinWaterLevel = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Parameters")
    float MaxWaterLevel = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Parameters")
    float MinTemperature = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Parameters")
    float MaxTemperature = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Parameters")
    float MinHeight = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Parameters")
    float MaxHeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    float BaseWeight = 1.0f;

    FBiomeArchetype()
    {
        BiomeType = EBiomeType::None;
        MinWaterLevel = 0.0f;
        MaxWaterLevel = 1.0f;
        MinTemperature = 0.0f;
        MaxTemperature = 1.0f;
        MinHeight = 0.0f;
        MaxHeight = 1.0f;
        BaseWeight = 1.0f;
    }
};


/**
 * 基础地图生成参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FBasicMapGenerationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 Width = 100;                  // 地图宽度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 Height = 100;                 // 地图高度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    EMapLayer MapLayer = EMapLayer::World; // 地图层级

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 Seed = 0;                     // 随机种子

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float NoiseScale = 0.01f;           // 噪声缩放

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    TArray<FBiomeArchetype> BiomeArchetypes; // 生态区原型配置

    FBasicMapGenerationParams()
    {
        Width = 100;
        Height = 100;
        MapLayer = EMapLayer::World;
        Seed = 0;
        NoiseScale = 0.01f;
    }
};

/**
 * 生态区权重结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FBiomeWeights
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    TMap<EBiomeType, float> Weights;    // 各生态区的权重

    FBiomeWeights()
    {
        // 默认权重为0
        for (int32 i = 0; i < static_cast<int32>(EBiomeType::MAX); ++i)
        {
            Weights.Add(static_cast<EBiomeType>(i), 0.0f);
        }
    }

    /**
     * 获取主导生态区
     */
    EBiomeType GetDominantBiome() const
    {
        EBiomeType DominantBiome = EBiomeType::None;
        float MaxWeight = 0.0f;

        for (const auto& WeightPair : Weights)
        {
            if (WeightPair.Value > MaxWeight)
            {
                MaxWeight = WeightPair.Value;
                DominantBiome = WeightPair.Key;
            }
        }

        return DominantBiome;
    }

    /**
     * 设置生态区权重
     */
    void SetBiomeWeight(EBiomeType BiomeType, float Weight)
    {
        Weights.Add(BiomeType, FMath::Clamp(Weight, 0.0f, 1.0f));
    }

    /**
     * 获取生态区权重
     */
    float GetBiomeWeight(EBiomeType BiomeType) const
    {
        const float* WeightPtr = Weights.Find(BiomeType);
        return WeightPtr ? *WeightPtr : 0.0f;
    }

    /**
     * 获取总权重
     */
    float GetTotalWeight() const
    {
        float Total = 0.0f;
        for (const auto& WeightPair : Weights)
        {
            Total += WeightPair.Value;
        }
        return Total;
    }

    /**
     * 归一化权重
     */
    void Normalize()
    {
        float Total = GetTotalWeight();
        if (Total > 0.0f)
        {
            for (auto& WeightPair : Weights)
            {
                WeightPair.Value /= Total;
            }
        }
    }
};


/**
 * 地图格子结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FMapCell

{
    GENERATED_BODY()

    // ========== 物理层 ==========
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Layer")
    float ZLevel = 0.0f;                // Z值高度（既是高度也是基本地图Z值，不影响渲染组件位置，地图保持平面）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Layer")
    float WaterLevel = 0.0f;            // 水量 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Layer")
    float Temperature = 0.5f;           // 温度 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Layer")
    ESoilType SoilType = ESoilType::Loam; // 土壤类型

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Layer")
    float Fertility = 0.5f;             // 肥力 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Layer")
    float MineralRichness = 0.0f;       // 矿藏丰度 (0.0-1.0)

    // ========== 生态区与动态 ==========
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ecology")
    FBiomeWeights BiomeWeights;         // 生态区权重

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ecology")
    float Biodiversity = 0.5f;          // 生物多样性指数 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ecology")
    float SuccessionRate = 0.1f;        // 生态演化速率

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ecology")
    float Disturbance = 0.0f;           // 外部干扰强度

    // ========== 地表覆盖 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface")
    ESurfaceCoverType SurfaceCoverType = ESurfaceCoverType::None; // 覆盖类型

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface")
    float SurfaceCoverThickness = 0.0f; // 覆盖厚度 (0.0-1.0)

    // ========== 风与气流 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float WindSpeed = 0.0f;             // 风速

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float WindDirX = 0.0f;              // 风向X分量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float WindDirY = 0.0f;              // 风向Y分量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float WindTurbulence = 0.0f;        // 湍流

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    bool bIsWindChannel = false;        // 是否为风口/风道

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float Visibility = 1.0f;            // 能见度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float FireSpreadRate = 0.0f;        // 火灾蔓延速率

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float TemperatureModifier = 0.0f;   // 风对温度修正

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float SoundPropagation = 1.0f;      // 声音传播距离

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float ParticleDispersion = 0.0f;    // 粉尘/花粉等扩散速率

    // ========== 元素/魔法 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Magic")
    FElementalWeights ElementalWeights; // 元素权重

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Magic")
    float MagicConcentration = 0.0f;    // 魔法能量浓度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Magic")
    float MagicFlowX = 0.0f;            // 魔法能量流向X分量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Magic")
    float MagicFlowY = 0.0f;            // 魔法能量流向Y分量

    // ========== 污染与腐化 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Corruption")
    float Pollution = 0.0f;             // 污染度 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Corruption")
    float Corruption = 0.0f;            // 腐化度 (0.0-1.0)

    // ========== 地下空间 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground")
    int32 UndergroundLayers = 0;        // 地下层数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground")
    bool bHasUndergroundSpace = false;  // 是否有地下空间

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Underground")
    EUndergroundType UndergroundType = EUndergroundType::None; // 地下空间类型

    // ========== 叙事与社会 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Narrative")
    int32 FactionID = 0;                // 派系ID

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Narrative")
    int32 ReligionID = 0;               // 信仰ID

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Narrative")
    float ControlStrength = 0.0f;       // 派系控制力 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Narrative")
    int32 Age = 0;                      // 年代

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Narrative")
    float ThreatLevel = 0.0f;           // 威胁等级 (0.0-1.0)

    // ========== 季节与气候 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    float SeasonalTemperature = 0.0f;   // 季节性温度修正

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    float SeasonalWater = 0.0f;         // 季节性水量修正

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    float ExtremeClimateChance = 0.0f;  // 极端气候事件概率

    // ========== 耐久度系统 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Durability")
    float MaxHealth = 100.0f;           // 最大血条（耐久度）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Durability")
    float CurrentHealth = 100.0f;       // 当前血条

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Building Layer")
    int32 RoomID = -1;                  // 房间ID（建筑层使用）

    // ========== 区域标识 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    int32 RegionID = -1;                // 区域ID（用于基础单元识别）

    FMapCell()
    {
        // 物理层初始化
        ZLevel = 0.0f;
        WaterLevel = 0.0f;
        Temperature = 0.5f;
        SoilType = ESoilType::Loam;
        Fertility = 0.5f;
        MineralRichness = 0.0f;

        // 生态层初始化
        Biodiversity = 0.5f;
        SuccessionRate = 0.1f;
        Disturbance = 0.0f;

        // 地表覆盖初始化
        SurfaceCoverType = ESurfaceCoverType::None;
        SurfaceCoverThickness = 0.0f;

        // 风与气流初始化
        WindSpeed = 0.0f;
        WindDirX = 0.0f;
        WindDirY = 0.0f;
        WindTurbulence = 0.0f;
        bIsWindChannel = false;
        Visibility = 1.0f;
        FireSpreadRate = 0.0f;
        TemperatureModifier = 0.0f;
        SoundPropagation = 1.0f;
        ParticleDispersion = 0.0f;

        // 魔法初始化
        MagicConcentration = 0.0f;
        MagicFlowX = 0.0f;
        MagicFlowY = 0.0f;

        // 污染与腐化初始化
        Pollution = 0.0f;
        Corruption = 0.0f;

        // 地下空间初始化
        UndergroundLayers = 0;
        bHasUndergroundSpace = false;
        UndergroundType = EUndergroundType::None;

        // 叙事层初始化
        FactionID = 0;
        ReligionID = 0;
        ControlStrength = 0.0f;
        Age = 0;
        ThreatLevel = 0.0f;

        // 季节与气候初始化
        SeasonalTemperature = 0.0f;
        SeasonalWater = 0.0f;
        ExtremeClimateChance = 0.0f;

        // 耐久度初始化
        MaxHealth = 100.0f;
        CurrentHealth = 100.0f;
        RoomID = -1;

        // 区域标识初始化
        RegionID = -1;
    }

    // 获取当前有效温度（包含季节修正）
    float GetEffectiveTemperature() const
    {
        return FMath::Clamp(Temperature + SeasonalTemperature + TemperatureModifier, 0.0f, 1.0f);
    }

    // 获取当前有效水量（包含季节修正）
    float GetEffectiveWaterLevel() const
    {
        return FMath::Clamp(WaterLevel + SeasonalWater, 0.0f, 1.0f);
    }

    // 获取地下空间的Z值
    float GetUndergroundZLevel() const;

    // 检查是否比另一个格子更低（用于水流计算）
    bool IsLowerThan(const FMapCell& OtherCell) const;

    // 检查水是否会流向另一个格子
    bool WaterFlowsTo(const FMapCell& OtherCell) const;

    // 获取风向向量
    FVector2D GetWindDirection() const
    {
        return FVector2D(WindDirX, WindDirY);
    }

    // 获取魔法流向向量
    FVector2D GetMagicFlow() const
    {
        return FVector2D(MagicFlowX, MagicFlowY);
    }

    // 计算血条（耐久度）
    float CalculateHealth() const
    {
        float BaseHealth = 100.0f;

        // 地形系数
        float TerrainMultiplier = 1.0f;
        switch (SoilType)
        {
            case ESoilType::Rock:
            case ESoilType::Bedrock:
                TerrainMultiplier = 2.0f;
                break;
            case ESoilType::Sand:
            case ESoilType::Peat:
                TerrainMultiplier = 0.5f;
                break;
            default:
                TerrainMultiplier = 1.0f;
                break;
        }

        // 覆盖层系数
        float CoverMultiplier = 1.0f + (SurfaceCoverThickness * 0.5f);

        // 魔法系数
        float MagicMultiplier = 1.0f + (MagicConcentration * 0.3f);

        // 污染/腐化减益
        float CorruptionMultiplier = 1.0f - (Pollution * 0.3f) - (Corruption * 0.5f);
        CorruptionMultiplier = FMath::Max(CorruptionMultiplier, 0.1f);

        return BaseHealth * TerrainMultiplier * CoverMultiplier * MagicMultiplier * CorruptionMultiplier;
    }

    // 是否被破坏
    bool IsDestroyed() const
    {
        return CurrentHealth <= 0.0f;
    }

    // 受到伤害
    void TakeDamage(float Damage)
    {
        CurrentHealth = FMath::Max(CurrentHealth - Damage, 0.0f);
    }

    // 修复
    void Repair(float Amount)
    {
        CurrentHealth = FMath::Min(CurrentHealth + Amount, MaxHealth);
    }
};

/**
 * 地图工具类
 * 集中管理所有地图相关的结构体、枚举和通用功能
 */
UCLASS(BlueprintType, Blueprintable)
class GAME_API UMapUtil : public UObject
{
    GENERATED_BODY()

public:
    // ========== 生态区相关工具函数 ==========

    /**
     * 根据物理参数计算生态区权重
     * @param Cell 地图格子
     * @param BiomeArchetypes 生态区原型配置
     * @return 计算后的生态区权重
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static FBiomeWeights CalculateBiomeWeights(const FMapCell& Cell, const TArray<FBiomeArchetype>& BiomeArchetypes);

    /**
     * 获取默认生态区原型配置
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static TArray<FBiomeArchetype> GetDefaultBiomeArchetypes();

    // ========== 物理参数工具函数 ==========

    /**
     * 计算两个格子之间的Z值差
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static float CalculateZLevelDifference(const FMapCell& CellA, const FMapCell& CellB);

    /**
     * 计算格子的移动阻力
     * @param Cell 地图格子
     * @param WindDirection 移动方向（用于风阻计算）
     * @return 阻力值 (0.0-1.0)
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static float CalculateMovementResistance(const FMapCell& Cell, const FVector2D& WindDirection = FVector2D::ZeroVector);

    // ========== 地图层级工具函数 ==========

    /**
     * 获取地图层级的缩放比例
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static float GetMapLayerScale(EMapLayer Layer);

    /**
     * 判断参数是否影响指定地图层级
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static bool DoesParameterAffectLayer(const FString& ParameterName, EMapLayer Layer);

    // ========== 字符串转换工具函数 ==========

    /**
     * 土壤类型转字符串
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static FString SoilTypeToString(ESoilType SoilType);

    /**
     * 地表覆盖类型转字符串
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static FString SurfaceCoverTypeToString(ESurfaceCoverType CoverType);

    /**
     * 生态区类型转字符串
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static FString BiomeTypeToString(EBiomeType BiomeType);

    /**
     * 地下空间类型转字符串
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static FString UndergroundTypeToString(EUndergroundType UndergroundType);

    // ========== 数学工具函数 ==========

    /**
     * 线性插值两个地图格子
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static FMapCell LerpMapCells(const FMapCell& A, const FMapCell& B, float Alpha);

    /**
     * 计算格子间的欧几里得距离
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static float CalculateDistance(int32 X1, int32 Y1, int32 X2, int32 Y2);

    /**
     * 计算格子间的曼哈顿距离
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static int32 CalculateManhattanDistance(int32 X1, int32 Y1, int32 X2, int32 Y2);

    // ========== 地图验证工具函数 ==========

    /**
     * 验证地图格子数据的有效性
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static bool ValidateMapCell(const FMapCell& Cell, FString& OutErrorMessage);



    // ========== 地图数据转换工具函数 ==========

    /**
     * 将地图格子转换为调试字符串
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static FString MapCellToDebugString(const FMapCell& Cell);

    /**
     * 从字符串解析生态区类型
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static EBiomeType StringToBiomeType(const FString& BiomeString);

    /**
     * 从字符串解析土壤类型
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static ESoilType StringToSoilType(const FString& SoilString);

    // ========== 地图统计工具函数 ==========

    /**
     * 计算地图区域的平均Z值
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static float CalculateAverageZLevel(const TArray<FMapCell>& Cells);

    /**
     * 计算地图区域的生态区分布统计
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static TMap<EBiomeType, float> CalculateBiomeDistribution(const TArray<FMapCell>& Cells);

    /**
     * 查找地图区域中的极值点（最高点、最低点等）
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static void FindExtremePoints(const TArray<FMapCell>& Cells, const TArray<FIntPoint>& Positions,
                                 FIntPoint& OutHighestPoint, FIntPoint& OutLowestPoint,
                                 FIntPoint& OutWettestPoint, FIntPoint& OutDriestPoint);

    // ========== 地图邻居查找工具函数 ==========

    /**
     * 获取四邻域坐标（上下左右）
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static TArray<FIntPoint> GetFourNeighbors(int32 X, int32 Y);

    /**
     * 获取八邻域坐标（包含对角线）
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static TArray<FIntPoint> GetEightNeighbors(int32 X, int32 Y);

    /**
     * 检查坐标是否在地图范围内
     */
    UFUNCTION(BlueprintCallable, Category = "Map Util")
    static bool IsValidMapPosition(int32 X, int32 Y, int32 MapWidth, int32 MapHeight);

    // ========== 地图常量定义 ==========

    // 默认地图尺寸
    static constexpr int32 DEFAULT_WORLD_MAP_SIZE = 200;
    static constexpr int32 DEFAULT_REGION_MAP_SIZE = 100;
    static constexpr int32 DEFAULT_BUILDING_MAP_SIZE = 50;

    // 物理参数范围
    static constexpr float MIN_HEIGHT = 0.0f;
    static constexpr float MAX_HEIGHT = 1000.0f;
    static constexpr float MIN_TEMPERATURE = -50.0f;
    static constexpr float MAX_TEMPERATURE = 100.0f;

    // 生态区权重阈值
    static constexpr float BIOME_WEIGHT_THRESHOLD = 0.1f;
    static constexpr float DOMINANT_BIOME_THRESHOLD = 0.5f;

    // 地图层级缩放比例
    static constexpr float WORLD_TO_REGION_SCALE = 0.1f;
    static constexpr float REGION_TO_BUILDING_SCALE = 0.1f;

private:
    // 私有辅助函数
    static float CalculateParameterMatch(float Value, float Min, float Max);
    static bool IsParameterInRange(float Value, float Min, float Max);
};













/**
 * 区域形状结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FRegionShape
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    TArray<FIntPoint> BoundaryPoints;   // 边界点

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    FIntPoint CenterPoint;              // 中心点

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    float Area = 0.0f;                  // 区域面积

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    EBiomeType DominantBiome = EBiomeType::None; // 主导生态区

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    int32 RegionID = -1;                // 区域ID

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    EMapLayer LayerType = EMapLayer::World; // 层级类型

    FRegionShape()
    {
        CenterPoint = FIntPoint::ZeroValue;
        Area = 0.0f;
        DominantBiome = EBiomeType::None;
        RegionID = -1;
        LayerType = EMapLayer::World;
    }

    // 通过边界点计算最小边界
    FIntPoint GetMinBounds() const
    {
        if (BoundaryPoints.Num() == 0) return FIntPoint::ZeroValue;

        FIntPoint MinBounds = BoundaryPoints[0];
        for (const FIntPoint& Point : BoundaryPoints)
        {
            MinBounds.X = FMath::Min(MinBounds.X, Point.X);
            MinBounds.Y = FMath::Min(MinBounds.Y, Point.Y);
        }
        return MinBounds;
    }

    // 通过边界点计算最大边界
    FIntPoint GetMaxBounds() const
    {
        if (BoundaryPoints.Num() == 0) return FIntPoint::ZeroValue;

        FIntPoint MaxBounds = BoundaryPoints[0];
        for (const FIntPoint& Point : BoundaryPoints)
        {
            MaxBounds.X = FMath::Max(MaxBounds.X, Point.X);
            MaxBounds.Y = FMath::Max(MaxBounds.Y, Point.Y);
        }
        return MaxBounds;
    }
};
/**
 * 生态区原型配置结构体
 * 定义每种生态区的物理参数范围
 */





/**
 * 玩家存在检测结果结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPlayerPresenceInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Presence")
    bool bPlayerPresent = false;        // 是否有玩家存在

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Presence")
    EMapLayer CurrentLayer = EMapLayer::World; // 当前层级

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Presence")
    FVector PlayerPosition = FVector::ZeroVector; // 玩家位置

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Presence")
    int32 PlayerCount = 0;              // 玩家数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Presence")
    float LastUpdateTime = 0.0f;        // 最后更新时间

    FPlayerPresenceInfo()
    {
        bPlayerPresent = false;
        CurrentLayer = EMapLayer::World;
        PlayerPosition = FVector::ZeroVector;
        PlayerCount = 0;
        LastUpdateTime = 0.0f;
    }
};

/**
 * 层级可视化状态结构体
 */


/**
 * 分割线类型枚举
 */
UENUM(BlueprintType)
enum class EDividerType : uint8
{
    None = 0,           // 无分割线
    RegionBorder,       // 区域边界
    BuildingBorder,     // 建筑边界
    Wall,               // 墙壁（房间层级边缘）
    MAX UMETA(Hidden)
};

/**
 * 分割线渲染组件结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FDividerRenderComponent
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Render Component")
    class USceneComponent* RenderComponent = nullptr; // 渲染组件

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Render Component")
    FIntPoint StartCoordinate = FIntPoint::ZeroValue; // 起始格子坐标

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Render Component")
    FIntPoint EndCoordinate = FIntPoint::ZeroValue; // 结束格子坐标

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Render Component")
    EDividerType DividerType = EDividerType::None; // 分割线类型

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Render Component")
    EMapLayer LayerType = EMapLayer::World; // 所属层级

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Render Component")
    bool bIsVisible = false;            // 是否可见

    FDividerRenderComponent()
    {
        RenderComponent = nullptr;
        StartCoordinate = FIntPoint::ZeroValue;
        EndCoordinate = FIntPoint::ZeroValue;
        DividerType = EDividerType::None;
        LayerType = EMapLayer::World;
        bIsVisible = false;
    }
};

/**
 * 格子渲染组件信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FCellRenderComponent
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Render Component")
    class USceneComponent* RenderComponent = nullptr; // 渲染组件

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Render Component")
    FIntPoint CellCoordinate = FIntPoint::ZeroValue; // 格子坐标

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Render Component")
    EMapLayer LayerType = EMapLayer::World; // 所属层级

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Render Component")
    bool bIsVisible = false;            // 是否可见

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Render Component")
    FMapCell CellData;                  // 格子数据

    FCellRenderComponent()
    {
        RenderComponent = nullptr;
        CellCoordinate = FIntPoint::ZeroValue;
        LayerType = EMapLayer::World;
        bIsVisible = false;
    }
};

/**
 * 可视化参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FVisualizationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    bool bEnablePlayerPresenceCheck = true; // 是否启用玩家存在检测

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float PlayerDetectionRadius = 1000.0f; // 玩家检测半径

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float UpdateFrequency = 0.1f;       // 更新频率（秒）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float FadeInDuration = 1.0f;        // 淡入持续时间

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float FadeOutDuration = 0.5f;       // 淡出持续时间

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float CellSize = 200.0f;            // 格子大小（UE单位）调整为更合适的大小

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float CellHeight = 10.0f;           // 格子高度（UE单位）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    TSubclassOf<class USceneComponent> DefaultRenderComponentClass; // 默认渲染组件类型（蓝图可设置）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    TSubclassOf<class USceneComponent> DividerRenderComponentClass; // 分割线渲染组件类型（蓝图可设置）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    TSubclassOf<class USceneComponent> WallRenderComponentClass; // 墙壁渲染组件类型（蓝图可设置）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    bool bRenderDividers = true;        // 是否渲染分割线

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    bool bRenderWalls = true;           // 是否渲染墙壁

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float DividerThickness = 2.0f;      // 分割线厚度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization Params")
    float WallHeight = 300.0f;          // 墙壁高度

    // ========== 分割线样式参数 ==========

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Style")
    float DividerLineWidth = 3.0f;     // 分割线宽度（UE单位）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Style")
    float DividerLineHeight = 15.0f;   // 分割线高度（UE单位）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Style")
    FLinearColor RegionBorderColor = FLinearColor(1.0f, 0.8f, 0.0f, 1.0f); // 区域边界颜色（金色）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Style")
    FLinearColor BuildingBorderColor = FLinearColor(0.0f, 0.7f, 1.0f, 1.0f); // 建筑边界颜色（蓝色）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Style")
    FLinearColor WallColor = FLinearColor(0.8f, 0.2f, 0.2f, 1.0f); // 墙壁颜色（红色）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Divider Style")
    TSoftObjectPtr<UStaticMesh> DividerLineMesh; // 自定义分割线网格资源

    bool bShowGridLines;

    bool bShowCellBorders;

    FVisualizationParams()
    {
        bEnablePlayerPresenceCheck = true;
        PlayerDetectionRadius = 1000.0f;
        UpdateFrequency = 0.1f;
        FadeInDuration = 1.0f;
        FadeOutDuration = 0.5f;
        bShowGridLines = true;
        bShowCellBorders = true;
        CellSize = 100.0f;
        CellHeight = 10.0f;
    }
};


/**
 * 格子血条计算参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FCellHealthParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    float BaseHealth = 100.0f;          // 基础血条

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    float WorldLayerMultiplier = 3.0f;  // 世界层血条倍数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    float RegionLayerMultiplier = 2.0f; // 区域层血条倍数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    float BuildingLayerMultiplier = 1.0f; // 建筑层血条倍数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    TMap<ESoilType, float> SoilTypeMultipliers; // 土壤类型系数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    TMap<EBiomeType, float> BiomeTypeMultipliers; // 生态区系数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    float MagicStrengthMultiplier = 1.5f; // 魔法强化系数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Params")
    float CorruptionWeakening = 0.5f;   // 腐化削弱系数

    FCellHealthParams()
    {
        BaseHealth = 100.0f;
        WorldLayerMultiplier = 3.0f;
        RegionLayerMultiplier = 2.0f;
        BuildingLayerMultiplier = 1.0f;
        MagicStrengthMultiplier = 1.5f;
        CorruptionWeakening = 0.5f;
        
        // 初始化默认土壤系数
        SoilTypeMultipliers.Add(ESoilType::Bedrock, 3.0f);
        SoilTypeMultipliers.Add(ESoilType::Rock, 2.5f);
        SoilTypeMultipliers.Add(ESoilType::Clay, 1.2f);
        SoilTypeMultipliers.Add(ESoilType::Loam, 1.0f);
        SoilTypeMultipliers.Add(ESoilType::Sand, 0.8f);
        SoilTypeMultipliers.Add(ESoilType::Peat, 0.6f);
        
        // 初始化默认生态区系数
        BiomeTypeMultipliers.Add(EBiomeType::Mountain, 2.0f);
        BiomeTypeMultipliers.Add(EBiomeType::Forest, 1.2f);
        BiomeTypeMultipliers.Add(EBiomeType::Grassland, 1.0f);
        BiomeTypeMultipliers.Add(EBiomeType::Desert, 0.9f);
        BiomeTypeMultipliers.Add(EBiomeType::Swamp, 0.7f);
        BiomeTypeMultipliers.Add(EBiomeType::Volcano, 1.5f);
    }
};