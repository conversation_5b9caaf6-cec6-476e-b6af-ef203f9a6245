<?xml version="1.0" encoding="utf-8"?>

<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <Name>Google Test</Name>

  <Location>Engine/Source/ThirdParty/ShaderConductor</Location>

  <Function>Google Test is used within ShaderConductor. ShaderConductor It's a tool designed for cross-compiling HLSL to other shading languages</Function>

  <Eula>https://github.com/google/googletest/blob/master/googletest/LICENSE</Eula>

  <RedistributeTo>

    <EndUserGroup>Licensees</EndUserGroup>

    <EndUserGroup>Git</EndUserGroup>

    <EndUserGroup>P4</EndUserGroup>

  </RedistributeTo>

  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/GoogleTest_License.txt</LicenseFolder>

</TpsData>