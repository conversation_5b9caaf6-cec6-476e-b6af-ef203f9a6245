<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>libtiff</Name>
  <Location></Location>
  <Function>It is used to import tiff files. We added a static library file (.lib on windows) and the required header to link against the object code. The lib is also linked statically against the zlib and turbojpeg libs found in our third parties.</Function>
  <Eula>https://gitlab.com/libtiff/libtiff/-/blob/v4.2.0/COPYRIGHT</Eula>
  <RedistributeTo>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>Engine/Source/ThirdParty/LibTiff</LicenseFolder>
</TpsData>