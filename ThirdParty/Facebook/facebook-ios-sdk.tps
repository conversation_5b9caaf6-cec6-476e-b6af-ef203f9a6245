<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Facebook SDK for iOS v17.0.3</Name>
  <Location>/Engine/Source/ThirdParty/Facebook/</Location>
  <Date>2022-10-21T12:00:20.8627086-04:00</Date>
  <Function>Allows users to integrate Facebook Login, Sharing etc into their game/app</Function>
  <Justification>External provider authentication of users and access to social features provided by Facebook</Justification>
  <Eula>https://github.com/facebook/facebook-ios-sdk/blob/releases/v17.0.3/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/FacebookSDKiOS_License.txt</LicenseFolder>
</TpsData>