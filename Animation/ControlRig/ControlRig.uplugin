{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Control Rig", "Description": "Framework for animation driven by user controls.", "Category": "Animation", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": true, "Installed": false, "Plugins": [{"Name": "RigVM", "Enabled": true}, {"Name": "PythonScriptPlugin", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "LevelSequenceEditor", "Enabled": true}, {"Name": "PropertyAccessEditor", "Enabled": true}, {"Name": "SequencerScripting", "Enabled": true}, {"Name": "TweeningUtils", "Enabled": true}], "Modules": [{"Name": "ControlRig", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "ControlRigDeveloper", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "ControlRigEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit"}]}