{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Actor Modifier Core", "Description": "Use modifier objects on actors to apply a custom behavior", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": true, "IsExperimentalVersion": false, "Installed": false, "PlatformAllowList": ["Win64", "Linux", "<PERSON>"], "Modules": [{"Name": "ActorModifierCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ActorModifierCoreBlueprint", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ActorModifierCoreEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "OperatorStack", "Enabled": true}]}