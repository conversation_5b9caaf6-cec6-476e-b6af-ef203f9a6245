// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Containers/UnrealString.h"

namespace UE::CPS::AddressPaths
{
// Requests/Responses
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GKeepAlive;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GStartSession;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GStopSession;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSubscribe;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GUnsubscribe;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GGetServerInformation;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GGetState;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GStartRecordingTake;
extern METAHUMANCAPTUREPROTOC<PERSON>STACK_API const TCHAR* GStopRecordingTake;
extern METAHUMANCAPTUREPROTOC<PERSON>STACK_API const TCHAR* GAbortRecordingTake;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GGetTakeList;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GGetTakeMetadata;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GExportTakeData;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GExportTakeVideoFrame;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GExportCameraFeedFrame;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GPauseExport;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GCancelExport;

// Updates
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSessionStopped;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GRecordingStatus;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTakeAdded;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTakeRemoved;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTakeUpdated;

// Platform Specific: IOS
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GDiskCapacity;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GBattery;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GThermalState;
}

namespace UE::CPS::Properties
{
// Keys
// Messages
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSessionId;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GAddressPath;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTransactionId;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTimestamp;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GType;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GBody;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GError;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GDescription;

// Requests
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSlateName;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTakeNumber;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSubject;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GScenario;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTags;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GNames;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTakeName;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GFiles;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GName;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GOffset;


// Responses
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GId;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GModel;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GPlatformName;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GPlatformVersion;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSoftwareName;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSoftwareVersion;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GExportPort;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GIsRecording;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GPlatformState;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTakes;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GDateTime;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GFrames;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GAppVersion;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GLength;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GVideo;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GFrameRate;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GHeight;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GWidth;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GAudio;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GChannels;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSampleRate;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GBitsPerChannel;

// Platform Specific: iOS
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTotalCapacity;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GRemainingCapacity;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GBatteryLevel;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GThermalState;

extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GTotal;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GRemaining;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GLevel;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GState;

// Values
// Messages
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GRequest;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GResponse;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GUpdate;

extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GIOS;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GAndroid;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GWindows;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GWindowsServer;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GLinux;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GMacOS;

// Platform Specific: iOS
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GNominal;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GFair;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GSerious;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GCritical;
}

namespace UE::CPS::ErrorNames
{
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GUnsupportedProtocolVersion;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GRequestFailed;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GRequestParsingFailed;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GNotSupported;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GNoSession;
extern METAHUMANCAPTUREPROTOCOLSTACK_API const TCHAR* GMissingBody;
}