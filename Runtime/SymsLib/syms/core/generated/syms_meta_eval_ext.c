// Copyright Epic Games, Inc. All Rights Reserved.
// generated
#ifndef _SYMS_META_EVAL_EXT_C
#define _SYMS_META_EVAL_EXT_C
//~ generated from code at syms/metaprogram/syms_metaprogram_eval.c:387
SYMS_GLOBAL SYMS_String8 syms_eval_opcode_strings[] = {
syms_str8_comp("Stop"),
syms_str8_comp("Cond"),
syms_str8_comp("Skip"),
syms_str8_comp("MemRead"),
syms_str8_comp("RegRead"),
syms_str8_comp("RegReadDyn"),
syms_str8_comp("FrameOff"),
syms_str8_comp("ModuleOff"),
syms_str8_comp("TLSOff"),
syms_str8_comp("ObjectOff"),
syms_str8_comp("CFA"),
syms_str8_comp("ConstU8"),
syms_str8_comp("ConstU16"),
syms_str8_comp("ConstU32"),
syms_str8_comp("ConstU64"),
syms_str8_comp("Abs"),
syms_str8_comp("Neg"),
syms_str8_comp("Add"),
syms_str8_comp("Sub"),
syms_str8_comp("Mul"),
syms_str8_comp("Div"),
syms_str8_comp("Mod"),
syms_str8_comp("LShift"),
syms_str8_comp("RShift"),
syms_str8_comp("BitAnd"),
syms_str8_comp("BitOr"),
syms_str8_comp("BitXor"),
syms_str8_comp("BitNot"),
syms_str8_comp("LogAnd"),
syms_str8_comp("LogOr"),
syms_str8_comp("LogNot"),
syms_str8_comp("EqEq"),
syms_str8_comp("NtEq"),
syms_str8_comp("LsEq"),
syms_str8_comp("GrEq"),
syms_str8_comp("Less"),
syms_str8_comp("Grtr"),
syms_str8_comp("Trunc"),
syms_str8_comp("TruncSigned"),
syms_str8_comp("Convert"),
syms_str8_comp("Pick"),
syms_str8_comp("Pop"),
syms_str8_comp("Insert"),
};
#endif
