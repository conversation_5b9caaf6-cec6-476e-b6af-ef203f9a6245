# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "renderSettingsPrimPath": {
                        "appliesTo": "layers", 
                        "default": "", 
                        "type": "string"
                    }
                }, 
                "Types": {
                    "UsdRenderPass": {
                        "alias": {
                            "UsdSchemaBase": "RenderPass"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaIdentifier": "RenderPass", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdRenderProduct": {
                        "alias": {
                            "UsdSchemaBase": "RenderProduct"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdRenderSettingsBase"
                        ], 
                        "schemaIdentifier": "RenderProduct", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdRenderSettings": {
                        "alias": {
                            "UsdSchemaBase": "RenderSettings"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdRenderSettingsBase"
                        ], 
                        "schemaIdentifier": "RenderSettings", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdRenderSettingsBase": {
                        "alias": {
                            "UsdSchemaBase": "RenderSettingsBase"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaIdentifier": "RenderSettingsBase", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdRenderVar": {
                        "alias": {
                            "UsdSchemaBase": "RenderVar"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaIdentifier": "RenderVar", 
                        "schemaKind": "concreteTyped"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdRender.dylib", 
            "Name": "usdRender", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
